#!/bin/bash

# Test script for Firebase Remote Config integration in emoji gallery
# This script deploys the app and monitors the complete data flow

echo "🚀 Testing Firebase Remote Config Integration for Emoji Gallery"
echo "=============================================================="

# Check if device is connected
echo "📱 Checking for connected devices..."
DEVICES=$(adb devices | grep -v "List of devices attached" | grep -v "^$" | wc -l)

if [ $DEVICES -eq 0 ]; then
    echo "❌ No devices connected. Please connect a device or start an emulator."
    echo "   You can start an emulator with: emulator -avd <avd_name>"
    exit 1
fi

echo "✅ Found $DEVICES device(s) connected"

# Install the app
echo ""
echo "📦 Installing the app..."
adb install -r app/build/outputs/apk/debug/app-debug.apk

if [ $? -ne 0 ]; then
    echo "❌ Failed to install the app"
    exit 1
fi

echo "✅ App installed successfully"

# Clear logs
echo ""
echo "🧹 Clearing logcat..."
adb logcat -c

# Start the app
echo ""
echo "🎯 Starting the app..."
adb shell am start -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.activity.splash.SplashActivity

# Wait a moment for the app to start
echo "⏳ Waiting for app to initialize..."
sleep 3

echo ""
echo "📊 Monitoring Remote Config Data Flow..."
echo "========================================"
echo "Looking for the following log patterns:"
echo "  1. EmojiCategoryService: Remote config fetching"
echo "  2. BatteryGalleryVM: StateFlow emission"
echo "  3. EmojiBatteryFragment: UI updates"
echo ""
echo "Press Ctrl+C to stop monitoring"
echo ""

# Monitor logs with comprehensive filtering
adb logcat | grep -E "(REMOTE_CONFIG|EmojiCategoryService|BatteryGalleryVM|EmojiBatteryFragment|CategoryAdapter)" --line-buffered | while read line; do
    # Color coding for different components
    if echo "$line" | grep -q "EmojiCategoryService"; then
        echo -e "\033[32m[SERVICE] $line\033[0m"  # Green for service
    elif echo "$line" | grep -q "BatteryGalleryVM"; then
        echo -e "\033[34m[VIEWMODEL] $line\033[0m"  # Blue for ViewModel
    elif echo "$line" | grep -q "EmojiBatteryFragment"; then
        echo -e "\033[35m[FRAGMENT] $line\033[0m"  # Magenta for Fragment
    elif echo "$line" | grep -q "CategoryAdapter"; then
        echo -e "\033[36m[ADAPTER] $line\033[0m"  # Cyan for Adapter
    else
        echo "$line"
    fi
done
